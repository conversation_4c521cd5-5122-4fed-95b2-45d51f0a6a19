2025-05-31 22:14:26,198 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:14:27,703 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:16:41,700 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:16:42,979 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:17:36,949 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:17:38,190 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:18:00,134 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 12, 强制删除: False, 演习模式: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 22:18:00,139 INFO: 找到实体: AdministrativeArea (ID: 12) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:217]
2025-05-31 22:18:00,139 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:231]
2025-05-31 22:18:00,140 INFO: 检查依赖: audit_logs -> AuditLog (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,142 INFO: 找到 0 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,142 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,143 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,144 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,145 INFO: 找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,145 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,146 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,146 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,147 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,147 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,148 INFO: 找到 2 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,148 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,149 INFO: 找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,149 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:18:00,151 INFO: 找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:18:00,151 INFO: 演习模式：模拟删除 AdministrativeArea (ID: 12) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:277]
2025-05-31 22:18:00,155 INFO: 演习模式：找到 0 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,156 INFO: 演习模式：找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,158 INFO: 演习模式：递归模拟删除子区域 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:307]
2025-05-31 22:18:00,160 INFO: 演习模式：找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,162 INFO: 演习模式：找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,164 INFO: 演习模式：找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,166 INFO: 演习模式：找到 2 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,175 INFO: 演习模式：找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:18:00,177 INFO: 演习模式：找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:23:49,491 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:25:05,684 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:26:44,730 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
