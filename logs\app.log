2025-05-31 21:00:39,734 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:00:41,208 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:00:43,419 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 21:00:43,423 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 21:01:10,987 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 33, 强制删除: False, 演习模式: False [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:145]
2025-05-31 21:01:10,989 INFO: 找到实体: AdministrativeArea (ID: 33) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:156]
2025-05-31 21:01:10,989 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:164]
2025-05-31 21:01:10,989 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:10,992 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:10,992 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:10,994 INFO: 找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:10,994 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:10,997 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:10,997 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:11,000 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:11,000 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:11,003 INFO: 找到 0 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:11,004 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:11,010 INFO: 找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:11,011 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:01:11,013 INFO: 找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:01:11,013 INFO: 开始处理依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:215]
2025-05-31 21:01:11,013 INFO: 处理级联删除: children -> AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,013 INFO: 准备删除 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,013 INFO: 处理级联删除: users -> User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,015 INFO: 准备删除 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,015 INFO: 删除依赖实体: User (ID: 21) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:240]
2025-05-31 21:01:11,015 INFO: 处理级联删除: menu_plans -> MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,015 INFO: 准备删除 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,015 INFO: 处理级联删除: consumption_plans -> ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,089 INFO: 准备删除 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,090 INFO: 处理级联删除: purchase_orders -> PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,090 INFO: 准备删除 0 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,090 INFO: 处理级联删除: warehouses -> Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,090 INFO: 准备删除 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,090 INFO: 处理级联删除: supplier_relations -> SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:223]
2025-05-31 21:01:11,091 INFO: 准备删除 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:235]
2025-05-31 21:01:11,091 INFO: 开始删除主实体: AdministrativeArea (ID: 33) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:250]
2025-05-31 21:01:11,108 INFO: 成功删除主实体: AdministrativeArea (ID: 33) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:254]
2025-05-31 21:03:17,611 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:03:20,119 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:03:48,279 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 21:03:48,282 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 21:05:38,760 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:05:51,949 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:06:03,128 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:06:04,488 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:06:09,702 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 21:06:09,703 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 21:07:26,757 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 2, 强制删除: False, 演习模式: False [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:145]
2025-05-31 21:07:26,758 INFO: 找到实体: AdministrativeArea (ID: 2) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:156]
2025-05-31 21:07:26,758 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:164]
2025-05-31 21:07:26,758 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:07:26,760 INFO: 找到 1 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:07:26,760 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:07:26,762 INFO: 找到 0 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
2025-05-31 21:07:26,763 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:171]
2025-05-31 21:07:26,765 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:184]
