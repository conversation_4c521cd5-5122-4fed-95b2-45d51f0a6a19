2025-05-31 21:50:09,542 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:50:10,867 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:51:17,693 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:51:18,914 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:51:29,562 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:52:48,605 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:54:09,321 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:57:55,438 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:59:22,321 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 12, 强制删除: False, 演习模式: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 21:59:22,322 INFO: 找到实体: AdministrativeArea (ID: 12) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:217]
2025-05-31 21:59:22,322 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:231]
2025-05-31 21:59:22,322 INFO: 检查依赖: audit_logs -> AuditLog (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,322 INFO: 找到 14 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,324 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,326 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,326 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,328 INFO: 找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,329 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,330 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,330 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,331 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,331 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,333 INFO: 找到 2 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,333 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,334 INFO: 找到 1 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,334 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:22,335 INFO: 找到 5 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:22,335 INFO: 演习模式：模拟删除 AdministrativeArea (ID: 12) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:277]
2025-05-31 21:59:22,336 INFO: 演习模式：找到 14 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,338 INFO: 演习模式：找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,338 INFO: 演习模式：递归模拟删除子区域 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:307]
2025-05-31 21:59:22,339 INFO: 演习模式：找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,339 INFO: 演习模式：找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,342 INFO: 演习模式：找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,346 INFO: 演习模式：找到 2 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,348 INFO: 演习模式：找到 1 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:22,348 INFO: 演习模式：找到 5 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 21:59:25,811 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 12, 强制删除: False, 演习模式: False [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 21:59:25,813 INFO: 找到实体: AdministrativeArea (ID: 12) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:217]
2025-05-31 21:59:25,814 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:231]
2025-05-31 21:59:25,814 INFO: 检查依赖: audit_logs -> AuditLog (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,815 INFO: 找到 14 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,817 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,817 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,817 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,819 INFO: 找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,819 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,820 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,820 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,821 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,821 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,821 INFO: 找到 2 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,822 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,822 INFO: 找到 1 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,822 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 21:59:25,823 INFO: 找到 5 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 21:59:25,823 INFO: 开始处理依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:341]
