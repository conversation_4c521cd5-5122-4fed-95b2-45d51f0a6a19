2025-05-31 21:19:07,444 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:19:08,647 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 21:19:24,691 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 2, 强制删除: False, 演习模式: False [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:182]
2025-05-31 21:19:24,692 INFO: 找到实体: AdministrativeArea (ID: 2) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 21:19:24,692 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:201]
2025-05-31 21:19:24,692 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,693 INFO: 找到 1 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,693 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,696 INFO: 找到 0 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,697 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,700 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,700 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,704 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,704 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,706 INFO: 找到 0 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,706 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,707 INFO: 找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,707 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:24,708 INFO: 找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:24,708 INFO: 开始处理依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:253]
2025-05-31 21:19:24,708 INFO: 处理级联删除: children -> AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,709 INFO: 准备删除 1 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,709 ERROR: 删除依赖实体 AdministrativeArea 时出错: 'InstrumentedList' object has no attribute 'id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:301]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py", line 285, in super_delete
    entity_ids = [str(entity.id) for entity in dependent_entities]
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py", line 285, in <listcomp>
    entity_ids = [str(entity.id) for entity in dependent_entities]
AttributeError: 'InstrumentedList' object has no attribute 'id'
2025-05-31 21:19:24,709 INFO: 处理级联删除: users -> User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,710 INFO: 准备删除 0 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,710 INFO: 处理级联删除: menu_plans -> MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,711 INFO: 准备删除 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,711 INFO: 处理级联删除: consumption_plans -> ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,711 INFO: 准备删除 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,711 INFO: 处理级联删除: purchase_orders -> PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,712 INFO: 准备删除 0 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,712 INFO: 处理级联删除: warehouses -> Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,712 INFO: 准备删除 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,712 INFO: 处理级联删除: supplier_relations -> SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:261]
2025-05-31 21:19:24,712 INFO: 准备删除 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:273]
2025-05-31 21:19:24,712 INFO: 开始删除主实体: AdministrativeArea (ID: 2) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:305]
2025-05-31 21:19:24,726 INFO: 成功删除主实体: AdministrativeArea (ID: 2) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:325]
2025-05-31 21:19:49,035 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 10, 强制删除: False, 演习模式: False [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:182]
2025-05-31 21:19:49,035 INFO: 找到实体: AdministrativeArea (ID: 10) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 21:19:49,035 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:201]
2025-05-31 21:19:49,035 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:49,037 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:49,037 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:49,038 INFO: 找到 0 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:49,038 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:49,039 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:49,039 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:49,040 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:49,040 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:49,041 INFO: 找到 0 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:49,041 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
2025-05-31 21:19:49,042 INFO: 找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:221]
2025-05-31 21:19:49,042 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:208]
