"""
数据管理路由模块
"""

from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.admin import admin_bp as admin_data_bp
from app.utils import admin_required
from app.utils.log_activity import log_activity
from app.admin.data_management import DataManagementService
from app.models import (
    User, Role, AdministrativeArea, Supplier, Recipe, Ingredient,
    MenuPlan, ConsumptionPlan, PurchaseOrder, StockIn, StockOut, Warehouse
)
from app.utils.super_delete import super_delete, get_entity_name, ENTITY_DEPENDENCY_GRAPH
import json
from datetime import datetime, date
import logging

@admin_data_bp.route('/')
@login_required
@admin_required
def index():
    """数据管理首页"""
    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management')
@login_required
@admin_required
def data_management():
    """数据管理页面"""
    # 获取数据库统计信息
    stats = DataManagementService.get_database_statistics()

    if not stats['success']:
        flash(stats['message'], 'danger')
        return redirect(url_for('system.users'))

    return render_template(
        'admin/data_management.html',
        title='系统数据管理',
        stats=stats['statistics'],
        now=datetime.now()
    )

@admin_data_bp.route('/data-management/clear-ingredient', methods=['POST'])
@login_required
@admin_required
def clear_ingredient_data():
    """清空食材数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-INGREDIENT-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 获取选项
    include_categories = request.form.get('include_categories') == 'on'

    # 执行清空操作
    result = DataManagementService.clear_ingredient_data(include_categories)

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='ingredient',
        description=f"清空食材数据 (包含分类: {include_categories})",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-recipe', methods=['POST'])
@login_required
@admin_required
def clear_recipe_data():
    """清空食谱数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-RECIPE-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 执行清空操作
    result = DataManagementService.clear_recipe_data()

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='recipe',
        description="清空食谱数据",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-supplier', methods=['POST'])
@login_required
@admin_required
def clear_supplier_data():
    """清空供应商数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-SUPPLIER-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 获取选项
    include_categories = request.form.get('include_categories') == 'on'

    # 执行清空操作
    result = DataManagementService.clear_supplier_data(include_categories)

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='supplier',
        description=f"清空供应商数据 (包含分类: {include_categories})",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-inventory', methods=['POST'])
@login_required
@admin_required
def clear_inventory_data():
    """清空库存数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-INVENTORY-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 获取选项
    include_locations = request.form.get('include_locations') == 'on'

    # 执行清空操作
    result = DataManagementService.clear_inventory_data(include_locations)

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='inventory',
        description=f"清空库存数据 (包含仓库和位置: {include_locations})",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-menu', methods=['POST'])
@login_required
@admin_required
def clear_menu_data():
    """清空菜单计划数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-MENU-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 执行清空操作
    result = DataManagementService.clear_menu_data()

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='menu',
        description="清空菜单计划数据",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-food-sample', methods=['POST'])
@login_required
@admin_required
def clear_food_sample_data():
    """清空留样数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-FOOD-SAMPLE-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 执行清空操作
    result = DataManagementService.clear_food_sample_data()

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='food_sample',
        description="清空留样数据",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-employee', methods=['POST'])
@login_required
@admin_required
def clear_employee_data():
    """清空员工数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-EMPLOYEE-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 执行清空操作
    result = DataManagementService.clear_employee_data()

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='employee',
        description="清空员工数据",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-notification', methods=['POST'])
@login_required
@admin_required
def clear_notification_data():
    """清空通知数据"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-NOTIFICATION-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 执行清空操作
    result = DataManagementService.clear_notification_data()

    # 记录审计日志
    log_activity(
        action='clear_data',
        resource_type='notification',
        description="清空通知数据",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/clear-audit-log', methods=['POST'])
@login_required
@admin_required
def clear_audit_log():
    """清空审计日志"""
    # 验证确认码
    confirmation_code = request.form.get('confirmation_code')
    if confirmation_code != 'CLEAR-AUDIT-LOG-DATA':
        flash('确认码不正确，操作已取消', 'danger')
        return redirect(url_for('admin_data.data_management'))

    # 执行清空操作
    result = DataManagementService.clear_audit_logs()

    # 记录新的审计日志
    log_activity(
        action='clear_data',
        resource_type='audit_log',
        description="清空审计日志",
        details=json.dumps(result['details'] if result['success'] else {'error': result['message']})
    )

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('admin_data.data_management'))

@admin_data_bp.route('/data-management/refresh-stats', methods=['GET'])
@login_required
@admin_required
def refresh_stats():
    """刷新数据库统计信息"""
    stats = DataManagementService.get_database_statistics()

    if stats['success']:
        return jsonify(stats['statistics'])
    else:
        return jsonify({'error': stats['message']}), 500

# 支持的实体类型映射
ENTITY_TYPES = {
    'supplier': Supplier,
    'recipe': Recipe,
    'ingredient': Ingredient,
    'menu_plan': MenuPlan,
    'consumption_plan': ConsumptionPlan,
    'purchase_order': PurchaseOrder,
    'stock_in': StockIn,
    'stock_out': StockOut,
    'warehouse': Warehouse,
    'area': AdministrativeArea,
    'user': User,
    'role': Role
}

# 实体类型的友好名称
ENTITY_TYPE_NAMES = {
    'supplier': '供应商',
    'recipe': '食谱',
    'ingredient': '食材',
    'menu_plan': '菜单计划',
    'consumption_plan': '消耗计划',
    'purchase_order': '采购订单',
    'stock_in': '入库单',
    'stock_out': '出库单',
    'warehouse': '仓库',
    'area': '区域',
    'user': '用户',
    'role': '角色'
}

@admin_data_bp.route('/super-delete')
@login_required
@admin_required
def super_delete_index():
    """超级删除功能首页"""
    # 检查权限
    if not current_user.has_permission('setting', 'super_delete'):
        flash('您没有使用超级删除功能的权限', 'danger')
        return redirect(url_for('admin_data.data_management'))

    return render_template('admin/super_delete/index.html',
                          entity_types=ENTITY_TYPE_NAMES,
                          title='超级删除工具')

@admin_data_bp.route('/super-delete/list-entities')
@login_required
@admin_required
def list_entities():
    """获取实体列表"""
    # 检查权限
    if not current_user.has_permission('setting', 'super_delete'):
        return jsonify({'error': '权限不足'}), 403

    entity_type_key = request.args.get('entity_type')

    if not entity_type_key or entity_type_key not in ENTITY_TYPES:
        return jsonify([])

    entity_type = ENTITY_TYPES[entity_type_key]
    results = []

    # 根据实体类型获取列表，使用原生SQL避免ORM问题
    try:
        from sqlalchemy import text
        from app import db

        # 限制返回数量，避免列表过长
        limit = 100

        # 根据实体类型获取表名和排序字段
        table_name_map = {
            'AdministrativeArea': ('administrative_areas', 'name'),
            'User': ('users', 'username'),
            'Supplier': ('suppliers', 'name'),
            'Recipe': ('recipes', 'name'),
            'Ingredient': ('ingredients', 'name'),
            'MenuPlan': ('menu_plans', 'id'),
            'ConsumptionPlan': ('consumption_plans', 'id'),
            'PurchaseOrder': ('purchase_orders', 'id'),
            'StockIn': ('stock_ins', 'id'),
            'StockOut': ('stock_outs', 'id'),
            'Warehouse': ('warehouses', 'name'),
            'Role': ('roles', 'name')
        }

        entity_name = entity_type.__name__
        if entity_name in table_name_map:
            table_name, order_field = table_name_map[entity_name]

            # 使用原生SQL查询
            sql = text(f"SELECT TOP {limit} id, {order_field} as name FROM {table_name} ORDER BY {order_field}")
            query_result = db.session.execute(sql)

            for row in query_result.fetchall():
                results.append({
                    'id': row[0],
                    'name': row[1] if row[1] else f"ID: {row[0]}"
                })
        else:
            logging.warning(f"未找到实体类型 {entity_name} 的表名映射")

    except Exception as e:
        # 使用已经导入的 logging 模块
        logging.error(f"获取实体列表出错: {str(e)}")

    return jsonify({'success': True, 'entities': results})

@admin_data_bp.route('/super-delete/search-entity')
@login_required
@admin_required
def search_entity():
    """搜索实体"""
    # 检查权限
    if not current_user.has_permission('setting', 'super_delete'):
        return jsonify({'error': '权限不足'}), 403
    entity_type_key = request.args.get('entity_type')
    query = request.args.get('query', '')

    if not entity_type_key or entity_type_key not in ENTITY_TYPES:
        return jsonify([])

    entity_type = ENTITY_TYPES[entity_type_key]
    results = []

    # 根据实体类型执行搜索
    if hasattr(entity_type, 'name'):
        entities = entity_type.query.filter(entity_type.name.like(f'%{query}%')).limit(10).all()
    elif hasattr(entity_type, 'title'):
        entities = entity_type.query.filter(entity_type.title.like(f'%{query}%')).limit(10).all()
    elif entity_type == User:
        entities = User.query.filter(
            (User.username.like(f'%{query}%')) |
            (User.real_name.like(f'%{query}%'))
        ).limit(10).all()
    else:
        # 默认只按ID搜索
        try:
            if query.isdigit():
                entity = entity_type.query.get(int(query))
                entities = [entity] if entity else []
            else:
                entities = []
        except:
            entities = []

    # 格式化结果
    for entity in entities:
        results.append({
            'id': entity.id,
            'name': get_entity_name(entity)
        })

    return jsonify(results)

@admin_data_bp.route('/super-delete/entity-details')
@login_required
@admin_required
def entity_details():
    """获取实体详情"""
    # 检查权限
    if not current_user.has_permission('setting', 'super_delete'):
        return jsonify({'success': False, 'message': '权限不足'}), 403
    entity_type_key = request.args.get('entity_type')
    entity_id = request.args.get('entity_id')

    if not entity_type_key or not entity_id:
        return jsonify({'success': False, 'message': '缺少必要参数'})

    if entity_type_key not in ENTITY_TYPES:
        return jsonify({'success': False, 'message': '不支持的实体类型'})

    try:
        entity_id = int(entity_id)
    except ValueError:
        return jsonify({'success': False, 'message': '实体ID必须是整数'})

    entity_type = ENTITY_TYPES[entity_type_key]
    entity = entity_type.query.get(entity_id)

    if not entity:
        return jsonify({'success': False, 'message': f'未找到ID为{entity_id}的{ENTITY_TYPE_NAMES[entity_type_key]}'})

    # 获取实体属性
    entity_attributes = {}
    for column in entity.__table__.columns:
        column_name = column.name
        column_value = getattr(entity, column_name)

        # 处理特殊类型
        if column_value is None:
            column_value = '无'
        elif isinstance(column_value, (datetime, date)):
            column_value = column_value.strftime('%Y-%m-%d %H:%M:%S')

        # 添加友好的列名
        friendly_name = column_name.replace('_', ' ').title()
        entity_attributes[friendly_name] = str(column_value)

    # 获取关联实体
    related_entities = []

    # 检查依赖关系
    if entity_type in ENTITY_DEPENDENCY_GRAPH:
        for relation_name, relation_info in ENTITY_DEPENDENCY_GRAPH[entity_type].items():
            dependent_model = relation_info['model']
            foreign_key = relation_info['fk']
            cascade = relation_info['cascade']

            # 查询依赖实体数量，使用原生SQL避免ORM问题
            count = 0
            try:
                # 根据模型获取表名
                table_name_map = {
                    'AdministrativeArea': 'administrative_areas',
                    'User': 'users',
                    'MenuPlan': 'menu_plans',
                    'ConsumptionPlan': 'consumption_plans',
                    'PurchaseOrder': 'purchase_orders',
                    'Warehouse': 'warehouses',
                    'SupplierSchoolRelation': 'supplier_school_relations',
                    'StockIn': 'stock_ins',
                    'StockOut': 'stock_outs',
                    'AuditLog': 'audit_logs'
                }

                dependent_table_name = table_name_map.get(dependent_model.__name__)
                if dependent_table_name:
                    # 使用原生SQL查询依赖实体数量
                    from sqlalchemy import text
                    from app import db
                    sql = text(f"SELECT COUNT(*) FROM {dependent_table_name} WHERE {foreign_key} = :entity_id")
                    query_result = db.session.execute(sql, {'entity_id': entity_id})
                    count = query_result.scalar()
                else:
                    count = 0
            except Exception as e:
                count = 0

            if count > 0:
                # 获取实体类型的友好名称
                entity_type_name = dependent_model.__name__
                for key, model in ENTITY_TYPES.items():
                    if model == dependent_model:
                        entity_type_name = ENTITY_TYPE_NAMES[key]
                        break

                # 添加关联信息
                related_entities.append({
                    'relation_type': relation_name.replace('_', ' ').title(),
                    'entity_type': entity_type_name,
                    'count': count,
                    'cascade': '是' if cascade else '否'
                })

    # 准备响应数据
    response = {
        'success': True,
        'entity_name': get_entity_name(entity),
        'entity_attributes': entity_attributes,
        'related_entities': related_entities
    }

    return jsonify(response)

@admin_data_bp.route('/super-delete/analyze', methods=['POST'])
@login_required
@admin_required
def analyze():
    """分析删除操作的影响"""
    # 检查权限
    if not current_user.has_permission('setting', 'super_delete'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    # Flask-WTF已经自动处理CSRF保护，不需要手动验证

    entity_type_key = request.form.get('entity_type')
    entity_id = request.form.get('entity_id')
    step_type = request.form.get('step_type')  # 分步删除类型
    force = request.form.get('force', 'false') == 'true'

    if not entity_type_key or not entity_id:
        return jsonify({'success': False, 'message': '缺少必要参数'})

    if entity_type_key not in ENTITY_TYPES:
        return jsonify({'success': False, 'message': '不支持的实体类型'})

    try:
        entity_id = int(entity_id)
    except ValueError:
        return jsonify({'success': False, 'message': '实体ID必须是整数'})

    entity_type = ENTITY_TYPES[entity_type_key]
    entity = entity_type.query.get(entity_id)

    if not entity:
        return jsonify({'success': False, 'message': f'未找到ID为{entity_id}的{ENTITY_TYPE_NAMES[entity_type_key]}'})

    # 执行删除分析，强制模式
    result = super_delete(entity_type, entity_id, current_user, True, dry_run=True)

    # 按步骤分类删除的实体
    steps_data = categorize_deletion_steps(result.deleted_entities, entity_type_key)

    # 准备响应数据
    response = {
        'success': result.success,
        'message': result.message,
        'entity_type': entity_type_key,
        'entity_id': entity_id,
        'entity_name': get_entity_name(entity),
        'deleted_entities': format_deleted_entities(result.deleted_entities),
        'blocked_by': format_blocked_entities(result.blocked_by),
        'steps_data': steps_data
    }

    return jsonify(response)

@admin_data_bp.route('/super-delete/execute', methods=['POST'])
@login_required
@admin_required
def execute():
    """执行删除操作"""
    # 检查权限
    if not current_user.has_permission('setting', 'super_delete'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    # Flask-WTF已经自动处理CSRF保护，不需要手动验证

    entity_type_key = request.form.get('entity_type')
    entity_id = request.form.get('entity_id')
    step_type = request.form.get('step_type')  # 分步删除类型
    force = request.form.get('force', 'false') == 'true'

    if not entity_type_key or not entity_id:
        return jsonify({'success': False, 'message': '缺少必要参数'})

    if entity_type_key not in ENTITY_TYPES:
        return jsonify({'success': False, 'message': '不支持的实体类型'})

    try:
        entity_id = int(entity_id)
    except ValueError:
        return jsonify({'success': False, 'message': '实体ID必须是整数'})

    entity_type = ENTITY_TYPES[entity_type_key]
    entity = entity_type.query.get(entity_id)

    if not entity:
        return jsonify({'success': False, 'message': f'未找到ID为{entity_id}的{ENTITY_TYPE_NAMES[entity_type_key]}'})

    # 如果是分步删除，执行分步删除逻辑
    if step_type:
        result = execute_step_deletion_simple(entity_type, entity_id, step_type, current_user, True)  # 强制删除
    else:
        # 执行完整删除操作，强制删除
        # 先禁用外键约束
        from sqlalchemy import text
        from app import db
        try:
            db.session.execute(text("EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL'"))
        except:
            pass

        result = super_delete(entity_type, entity_id, current_user, True, dry_run=False)  # 强制删除

        # 重新启用外键约束
        try:
            db.session.execute(text("EXEC sp_MSforeachtable 'ALTER TABLE ? WITH CHECK CHECK CONSTRAINT ALL'"))
        except:
            pass

    # 记录审计日志
    log_activity(
        action='super_delete',
        resource_type=entity_type_key,
        description=f"超级删除 {ENTITY_TYPE_NAMES[entity_type_key]} (ID: {entity_id})",
        details=json.dumps({
            'success': result.success,
            'force': force,
            'deleted_entities': {k: v for k, v in result.deleted_entities.items()} if result.success else {},
            'failed_entities': {k: v for k, v in result.failed_entities.items()} if not result.success else {}
        })
    )

    # 准备响应数据
    response = {
        'success': result.success,
        'message': result.message,
        'entity_type': entity_type_key,
        'entity_id': entity_id,
        'deleted_entities': format_deleted_entities(result.deleted_entities),
        'failed_entities': format_failed_entities(result.failed_entities)
    }

    return jsonify(response)

def execute_step_deletion_simple(entity_type, entity_id, step_type, current_user=None, force=False):
    """简单的分步删除实现 - 暴力删除"""
    from app.utils.super_delete import SuperDeleteResult
    from sqlalchemy import text
    from app import db
    import logging

    result = SuperDeleteResult()
    logger = logging.getLogger(__name__)

    # 直接用SQL暴力删除，按步骤分类
    step_sql_map = {
        'child_areas': [
            f"DELETE FROM administrative_areas WHERE parent_id = {entity_id}"
        ],
        'users': [
            f"DELETE FROM users WHERE area_id = {entity_id}"
        ],
        'supplier_relations': [
            f"DELETE FROM supplier_school_relations WHERE area_id = {entity_id}"
        ],
        'menu_plans': [
            f"DELETE FROM consumption_details WHERE consumption_plan_id IN (SELECT id FROM consumption_plans WHERE area_id = {entity_id})",
            f"DELETE FROM consumption_plans WHERE area_id = {entity_id}",
            f"DELETE FROM menu_recipes WHERE menu_plan_id IN (SELECT id FROM menu_plans WHERE area_id = {entity_id})",
            f"DELETE FROM menu_plans WHERE area_id = {entity_id}"
        ],
        'purchase_stock': [
            f"DELETE FROM stock_in_items WHERE purchase_order_item_id IN (SELECT poi.id FROM purchase_order_items poi JOIN purchase_orders po ON poi.purchase_order_id = po.id WHERE po.area_id = {entity_id})",
            f"DELETE FROM purchase_order_items WHERE purchase_order_id IN (SELECT id FROM purchase_orders WHERE area_id = {entity_id})",
            f"DELETE FROM purchase_orders WHERE area_id = {entity_id}",
            f"DELETE FROM warehouses WHERE area_id = {entity_id}"
        ],
        'audit_logs': [
            f"DELETE FROM audit_logs WHERE area_id = {entity_id}"
        ]
    }

    if step_type not in step_map:
        result.success = False
        result.message = f"不支持的步骤: {step_type}"
        return result

    try:
        # 直接强制删除，不管任何约束
        target_relations = step_map[step_type]

        if entity_type in ENTITY_DEPENDENCY_GRAPH:
            for relation_name, relation_info in ENTITY_DEPENDENCY_GRAPH[entity_type].items():
                if relation_name not in target_relations:
                    continue

                dependent_model = relation_info['model']
                foreign_key = relation_info['fk']

                # 查询要删除的实体
                dependent_table_name = ENTITY_TABLE_MAPPING.get(dependent_model)
                if dependent_table_name:
                    try:
                        sql = text(f"SELECT id FROM {dependent_table_name} WHERE {foreign_key} = :entity_id")
                        query_result = db.session.execute(sql, {'entity_id': entity_id})
                        dependent_ids = [row[0] for row in query_result.fetchall()]

                        if dependent_ids:
                            # 强制删除 - 先删除所有相关子表
                            ids_str = ','.join([str(id) for id in dependent_ids])

                            # 特殊处理：删除采购订单时，先删除所有相关表
                            if dependent_table_name == 'purchase_orders':
                                # 删除所有相关的子表数据
                                db.session.execute(text(f"DELETE FROM stock_in_items WHERE purchase_order_item_id IN (SELECT id FROM purchase_order_items WHERE purchase_order_id IN ({ids_str}))"))
                                db.session.execute(text(f"DELETE FROM purchase_order_items WHERE purchase_order_id IN ({ids_str})"))

                            # 删除主表
                            delete_sql = text(f"DELETE FROM {dependent_table_name} WHERE id IN ({ids_str})")
                            result_count = db.session.execute(delete_sql)

                            logger.info(f"强制删除了 {len(dependent_ids)} 个 {dependent_model.__name__} 记录")

                            # 记录删除结果
                            for dep_id in dependent_ids:
                                result.add_deleted(dependent_model, dep_id)

                    except Exception as e:
                        # 忽略所有错误，继续下一个
                        logger.warning(f"删除 {dependent_model.__name__} 时出错，忽略: {str(e)}")
                        continue

        db.session.commit()
        result.success = True
        result.message = f"强制删除步骤完成: {step_type}"

    except Exception as e:
        db.session.rollback()
        result.success = False
        result.message = f"删除失败: {str(e)}"
        logger.error(f"分步删除错误: {str(e)}")

    return result

def format_deleted_entities(deleted_entities):
    """格式化已删除的实体信息"""
    result = []
    for entity_type, entity_ids in deleted_entities.items():
        type_name = next((v for k, v in ENTITY_TYPE_NAMES.items() if ENTITY_TYPES[k].__name__ == entity_type), entity_type)
        result.append({
            'type': entity_type,
            'type_name': type_name,
            'count': len(entity_ids),
            'ids': entity_ids
        })
    return result

def format_blocked_entities(blocked_by):
    """格式化被阻止删除的实体信息"""
    result = []
    for entity_type, entities in blocked_by.items():
        type_name = next((v for k, v in ENTITY_TYPE_NAMES.items() if ENTITY_TYPES[k].__name__ == entity_type), entity_type)
        for entity_id, blocking_info in entities.items():
            result.append({
                'type': entity_type,
                'type_name': type_name,
                'id': entity_id,
                'blocking_info': blocking_info
            })
    return result

def format_failed_entities(failed_entities):
    """格式化删除失败的实体信息"""
    result = []
    for entity_type, entities in failed_entities.items():
        type_name = next((v for k, v in ENTITY_TYPE_NAMES.items() if ENTITY_TYPES[k].__name__ == entity_type), entity_type)
        for entity_id, reason in entities.items():
            result.append({
                'type': entity_type,
                'type_name': type_name,
                'id': entity_id,
                'reason': reason
            })
    return result

def categorize_deletion_steps(deleted_entities, main_entity_type):
    """按逻辑步骤分类删除的实体"""
    steps = {
        'child_areas': {'name': '子区域', 'entities': [], 'count': 0},
        'users': {'name': '用户', 'entities': [], 'count': 0},
        'supplier_relations': {'name': '供应商关系', 'entities': [], 'count': 0},
        'menu_plans': {'name': '菜单计划', 'entities': [], 'count': 0},
        'purchase_stock': {'name': '采购和库存', 'entities': [], 'count': 0},
        'audit_logs': {'name': '审计日志', 'entities': [], 'count': 0}
    }

    for entity_type, entity_ids in deleted_entities.items():
        type_name = next((v for k, v in ENTITY_TYPE_NAMES.items() if ENTITY_TYPES[k].__name__ == entity_type), entity_type)
        count = len(entity_ids)

        # 按实体类型分类到不同步骤
        if entity_type == 'AdministrativeArea' and main_entity_type == 'area':
            steps['child_areas']['entities'].append({'type_name': type_name, 'count': count, 'ids': entity_ids})
            steps['child_areas']['count'] += count
        elif entity_type == 'User':
            steps['users']['entities'].append({'type_name': type_name, 'count': count, 'ids': entity_ids})
            steps['users']['count'] += count
        elif entity_type == 'SupplierSchoolRelation':
            steps['supplier_relations']['entities'].append({'type_name': type_name, 'count': count, 'ids': entity_ids})
            steps['supplier_relations']['count'] += count
        elif entity_type in ['MenuPlan', 'ConsumptionPlan']:
            steps['menu_plans']['entities'].append({'type_name': type_name, 'count': count, 'ids': entity_ids})
            steps['menu_plans']['count'] += count
        elif entity_type in ['PurchaseOrder', 'StockIn', 'StockOut', 'Warehouse', 'StorageLocation', 'Inventory']:
            steps['purchase_stock']['entities'].append({'type_name': type_name, 'count': count, 'ids': entity_ids})
            steps['purchase_stock']['count'] += count
        elif entity_type == 'AuditLog':
            steps['audit_logs']['entities'].append({'type_name': type_name, 'count': count, 'ids': entity_ids})
            steps['audit_logs']['count'] += count

    return steps
