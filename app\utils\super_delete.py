"""
超级删除功能 - 根据数据联动关系安全删除数据

此模块提供了超级删除功能的核心实现，能够根据系统中的数据联动关系，
安全地删除数据，确保数据一致性和完整性。
"""

from app import db
from app.models import (
    User, Role, AdministrativeArea,
    IngredientCategory, Ingredient,
    RecipeCategory, Recipe, RecipeIngredient, RecipeProcess,
    SupplierCategory, Supplier, SupplierProduct, SupplierCertificate, SupplierSchoolRelation,
    PurchaseOrder, PurchaseOrderItem, SupplierDelivery, DeliveryItem,
    Warehouse, StorageLocation, StockIn, StockInItem, StockOut, StockOutItem, Inventory,
    MenuPlan, MenuRecipe, ConsumptionPlan, ConsumptionDetail, FoodSample,
    AuditLog, Notification
)
from sqlalchemy.exc import IntegrityError
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义实体之间的依赖关系
# 格式: {实体类: {关联字段: {'model': 依赖实体类, 'fk': 外键字段, 'cascade': 是否级联删除, 'required': 是否必须删除}}}
ENTITY_DEPENDENCY_GRAPH = {
    Supplier: {
        'certificates': {'model': SupplierCertificate, 'fk': 'supplier_id', 'cascade': True, 'required': True},
        'products': {'model': SupplierProduct, 'fk': 'supplier_id', 'cascade': True, 'required': True},
        'school_relations': {'model': SupplierSchoolRelation, 'fk': 'supplier_id', 'cascade': True, 'required': True},
        'purchase_orders': {'model': PurchaseOrder, 'fk': 'supplier_id', 'cascade': False, 'required': False},
        'deliveries': {'model': SupplierDelivery, 'fk': 'supplier_id', 'cascade': False, 'required': False}
    },
    Recipe: {
        'ingredients': {'model': RecipeIngredient, 'fk': 'recipe_id', 'cascade': True, 'required': True},
        'processes': {'model': RecipeProcess, 'fk': 'recipe_id', 'cascade': True, 'required': True},
        'menu_recipes': {'model': MenuRecipe, 'fk': 'recipe_id', 'cascade': False, 'required': False},
        'food_samples': {'model': FoodSample, 'fk': 'recipe_id', 'cascade': False, 'required': False}
    },
    Ingredient: {
        'recipe_ingredients': {'model': RecipeIngredient, 'fk': 'ingredient_id', 'cascade': False, 'required': False},
        'supplier_products': {'model': SupplierProduct, 'fk': 'ingredient_id', 'cascade': False, 'required': False},
        'purchase_order_items': {'model': PurchaseOrderItem, 'fk': 'ingredient_id', 'cascade': False, 'required': False},
        'inventories': {'model': Inventory, 'fk': 'ingredient_id', 'cascade': False, 'required': False},
        'consumption_details': {'model': ConsumptionDetail, 'fk': 'ingredient_id', 'cascade': False, 'required': False}
    },
    MenuPlan: {
        'menu_recipes': {'model': MenuRecipe, 'fk': 'menu_plan_id', 'cascade': True, 'required': True},
        'consumption_plans': {'model': ConsumptionPlan, 'fk': 'menu_plan_id', 'cascade': True, 'required': True},
        'food_samples': {'model': FoodSample, 'fk': 'menu_plan_id', 'cascade': False, 'required': False}
    },
    ConsumptionPlan: {
        'details': {'model': ConsumptionDetail, 'fk': 'consumption_plan_id', 'cascade': True, 'required': True},
        'stock_outs': {'model': StockOut, 'fk': 'consumption_plan_id', 'cascade': False, 'required': False}
    },
    PurchaseOrder: {
        'order_items': {'model': PurchaseOrderItem, 'fk': 'order_id', 'cascade': True, 'required': True},
        'deliveries': {'model': SupplierDelivery, 'fk': 'order_id', 'cascade': False, 'required': False},
        'stock_ins': {'model': StockIn, 'fk': 'order_id', 'cascade': False, 'required': False}
    },
    StockIn: {
        'items': {'model': StockInItem, 'fk': 'stock_in_id', 'cascade': True, 'required': True}
    },
    StockOut: {
        'items': {'model': StockOutItem, 'fk': 'stock_out_id', 'cascade': True, 'required': True}
    },
    Warehouse: {
        'storage_locations': {'model': StorageLocation, 'fk': 'warehouse_id', 'cascade': True, 'required': True},
        'inventories': {'model': Inventory, 'fk': 'warehouse_id', 'cascade': False, 'required': False},
        'stock_ins': {'model': StockIn, 'fk': 'warehouse_id', 'cascade': False, 'required': False},
        'stock_outs': {'model': StockOut, 'fk': 'warehouse_id', 'cascade': False, 'required': False}
    },
    User: {
        'menu_plans': {'model': MenuPlan, 'fk': 'created_by', 'cascade': False, 'required': False},
        'consumption_plans': {'model': ConsumptionPlan, 'fk': 'created_by', 'cascade': False, 'required': False},
        'purchase_orders': {'model': PurchaseOrder, 'fk': 'created_by', 'cascade': False, 'required': False},
        'stock_ins': {'model': StockIn, 'fk': 'created_by', 'cascade': False, 'required': False},
        'stock_outs': {'model': StockOut, 'fk': 'created_by', 'cascade': False, 'required': False},
        'audit_logs': {'model': AuditLog, 'fk': 'user_id', 'cascade': True, 'required': True}
    },
    AdministrativeArea: {
        'children': {'model': AdministrativeArea, 'fk': 'parent_id', 'cascade': True, 'required': True},
        'users': {'model': User, 'fk': 'area_id', 'cascade': True, 'required': True},
        'menu_plans': {'model': MenuPlan, 'fk': 'area_id', 'cascade': True, 'required': True},
        'consumption_plans': {'model': ConsumptionPlan, 'fk': 'area_id', 'cascade': True, 'required': True},
        'purchase_orders': {'model': PurchaseOrder, 'fk': 'area_id', 'cascade': True, 'required': True},
        'warehouses': {'model': Warehouse, 'fk': 'area_id', 'cascade': True, 'required': True},
        'supplier_relations': {'model': SupplierSchoolRelation, 'fk': 'area_id', 'cascade': True, 'required': True},
        'audit_logs': {'model': AuditLog, 'fk': 'area_id', 'cascade': True, 'required': True}
    }
}

# 实体类到表名的映射
ENTITY_TABLE_MAPPING = {
    User: 'users',
    Role: 'roles',
    AdministrativeArea: 'administrative_areas',
    IngredientCategory: 'ingredient_categories',
    Ingredient: 'ingredients',
    RecipeCategory: 'recipe_categories',
    Recipe: 'recipes',
    RecipeIngredient: 'recipe_ingredients',
    RecipeProcess: 'recipe_processes',
    SupplierCategory: 'supplier_categories',
    Supplier: 'suppliers',
    SupplierProduct: 'supplier_products',
    SupplierCertificate: 'supplier_certificates',
    SupplierSchoolRelation: 'supplier_school_relations',
    PurchaseOrder: 'purchase_orders',
    PurchaseOrderItem: 'purchase_order_items',
    SupplierDelivery: 'supplier_deliveries',
    DeliveryItem: 'delivery_items',
    Warehouse: 'warehouses',
    StorageLocation: 'storage_locations',
    StockIn: 'stock_ins',
    StockInItem: 'stock_in_items',
    StockOut: 'stock_outs',
    StockOutItem: 'stock_out_items',
    Inventory: 'inventories',
    MenuPlan: 'menu_plans',
    MenuRecipe: 'menu_recipes',
    ConsumptionPlan: 'consumption_plans',
    ConsumptionDetail: 'consumption_details',
    FoodSample: 'food_samples',
    AuditLog: 'audit_logs',
    Notification: 'notifications'
}

# 超级删除结果类
class SuperDeleteResult:
    """超级删除结果类"""
    def __init__(self):
        self.success = True
        self.message = ""
        self.deleted_entities = {}  # {实体类名: [实体ID列表]}
        self.failed_entities = {}   # {实体类名: {实体ID: 失败原因}}
        self.blocked_by = {}        # {实体类名: {实体ID: [阻止删除的实体信息]}}

    def add_deleted(self, entity_type, entity_id):
        """添加已删除的实体"""
        type_name = entity_type.__name__
        if type_name not in self.deleted_entities:
            self.deleted_entities[type_name] = []
        self.deleted_entities[type_name].append(entity_id)

    def add_failed(self, entity_type, entity_id, reason):
        """添加删除失败的实体"""
        type_name = entity_type.__name__
        if type_name not in self.failed_entities:
            self.failed_entities[type_name] = {}
        self.failed_entities[type_name][entity_id] = reason

    def add_blocked(self, entity_type, entity_id, blocking_info):
        """添加被阻止删除的实体"""
        type_name = entity_type.__name__
        if type_name not in self.blocked_by:
            self.blocked_by[type_name] = {}
        self.blocked_by[type_name][entity_id] = blocking_info

def get_entity_name(entity):
    """获取实体的名称或标识信息"""
    if hasattr(entity, 'name'):
        return entity.name
    elif hasattr(entity, 'title'):
        return entity.title
    elif hasattr(entity, 'username'):
        return f"{entity.username} ({entity.real_name or ''})"
    elif hasattr(entity, 'code'):
        return entity.code
    else:
        return f"ID: {entity.id}"

def super_delete(entity_type, entity_id, current_user=None, force=False, dry_run=False):
    """
    超级删除函数

    Args:
        entity_type: 实体类型（类）
        entity_id: 实体ID
        current_user: 当前用户对象
        force: 是否强制删除
        dry_run: 是否只分析不实际删除

    Returns:
        SuperDeleteResult: 删除结果对象
    """
    result = SuperDeleteResult()
    logger.info(f"开始删除操作 - 实体类型: {entity_type.__name__}, ID: {entity_id}, 强制删除: {force}, 演习模式: {dry_run}")

    # 使用原生SQL检查实体是否存在，避免ORM autoflush
    try:
        table_name = ENTITY_TABLE_MAPPING.get(entity_type)
        if not table_name:
            error_msg = f"未找到 {entity_type.__name__} 的表名映射"
            logger.error(error_msg)
            result.success = False
            result.message = error_msg
            return result

        # 使用原生SQL检查实体是否存在
        sql = text(f"SELECT COUNT(*) FROM {table_name} WHERE id = :entity_id")
        query_result = db.session.execute(sql, {'entity_id': entity_id})
        entity_exists = query_result.scalar() > 0

        if not entity_exists:
            error_msg = f"未找到ID为{entity_id}的{entity_type.__name__}"
            logger.error(error_msg)
            result.success = False
            result.message = error_msg
            return result

        logger.info(f"找到实体: {entity_type.__name__} (ID: {entity_id})")
    except Exception as e:
        error_msg = f"检查实体存在性时出错: {str(e)}"
        logger.error(error_msg)
        result.success = False
        result.message = error_msg
        return result

    # 检查是否可以删除
    can_delete = True
    blocking_info = []

    # 检查依赖关系
    if entity_type in ENTITY_DEPENDENCY_GRAPH:
        logger.info(f"开始检查依赖关系 - {entity_type.__name__}")
        for relation_name, relation_info in ENTITY_DEPENDENCY_GRAPH[entity_type].items():
            dependent_model = relation_info['model']
            foreign_key = relation_info['fk']
            cascade = relation_info['cascade']
            required = relation_info['required']
            
            logger.info(f"检查依赖: {relation_name} -> {dependent_model.__name__} (级联: {cascade}, 必需: {required})")

            # 使用原生SQL查询依赖实体数量，避免ORM autoflush
            dependent_count = 0
            try:
                # 获取依赖实体的表名
                dependent_table_name = ENTITY_TABLE_MAPPING.get(dependent_model)
                if dependent_table_name:
                    # 使用原生SQL查询依赖实体数量
                    sql = text(f"SELECT COUNT(*) FROM {dependent_table_name} WHERE {foreign_key} = :entity_id")
                    query_result = db.session.execute(sql, {'entity_id': entity_id})
                    dependent_count = query_result.scalar()
                else:
                    logger.warning(f"未找到 {dependent_model.__name__} 的表名映射，跳过依赖检查")
            except Exception as e:
                logger.error(f"检查依赖实体时出错: {str(e)}")
                dependent_count = 0

            logger.info(f"找到 {dependent_count} 个依赖实体: {dependent_model.__name__}")

            # 如果有依赖实体且不允许级联删除，则阻止删除
            if dependent_count > 0 and not cascade:
                if required or not force:
                    can_delete = False
                    blocking_msg = f"存在{dependent_count}个关联的{dependent_model.__name__}"
                    logger.warning(f"阻止删除: {blocking_msg}")
                    blocking_info.append(blocking_msg)

    # 如果不能删除，返回阻止原因
    if not can_delete and not force:
        error_msg = f"无法删除 {entity_type.__name__} (ID: {entity_id})，存在阻止删除的依赖关系"
        logger.error(error_msg)
        result.success = False
        result.message = error_msg
        result.add_blocked(entity_type, entity_id, blocking_info)
        return result

    # 如果是演习模式，只返回分析结果
    if dry_run:
        logger.info(f"演习模式：可以删除 {entity_type.__name__} (ID: {entity_id})")
        result.success = True
        result.message = f"演习模式：可以删除 {entity_type.__name__} (ID: {entity_id})"
        result.add_deleted(entity_type, entity_id)
        return result

    # 开始事务，使用no_autoflush避免自动刷新导致的问题
    try:
        with db.session.no_autoflush:
            # 处理依赖关系
            if entity_type in ENTITY_DEPENDENCY_GRAPH:
                logger.info(f"开始处理依赖关系 - {entity_type.__name__}")
                for relation_name, relation_info in ENTITY_DEPENDENCY_GRAPH[entity_type].items():
                    dependent_model = relation_info['model']
                    foreign_key = relation_info['fk']
                    cascade = relation_info['cascade']

                    # 只处理允许级联删除的关系
                    if cascade:
                        logger.info(f"处理级联删除: {relation_name} -> {dependent_model.__name__}")
                        # 使用原生SQL查询依赖实体，避免ORM autoflush
                        dependent_entity_ids = []
                        try:
                            # 获取依赖实体的表名
                            dependent_table_name = ENTITY_TABLE_MAPPING.get(dependent_model)
                            if dependent_table_name:
                                # 使用原生SQL查询依赖实体的ID
                                sql = text(f"SELECT id FROM {dependent_table_name} WHERE {foreign_key} = :entity_id")
                                query_result = db.session.execute(sql, {'entity_id': entity_id})
                                dependent_entity_ids = [row[0] for row in query_result.fetchall()]
                                logger.info(f"使用SQL查询到 {len(dependent_entity_ids)} 个依赖实体ID: {dependent_model.__name__}")
                            else:
                                logger.warning(f"未找到 {dependent_model.__name__} 的表名映射，跳过")
                        except Exception as e:
                            logger.error(f"查询依赖实体时出错: {str(e)}")
                            dependent_entity_ids = []
                    
                        logger.info(f"准备删除 {len(dependent_entity_ids)} 个依赖实体: {dependent_model.__name__}")

                        # 特殊处理：如果是删除区域的子区域，需要递归删除
                        if dependent_model == AdministrativeArea and relation_name == 'children':
                            logger.info(f"开始递归删除子区域")
                            for child_id in dependent_entity_ids:
                                try:
                                    # 递归调用超级删除来删除子区域
                                    child_result = super_delete(AdministrativeArea, child_id, current_user, force, dry_run=False)
                                    if child_result.success:
                                        logger.info(f"成功递归删除子区域: {child_id}")
                                        # 合并删除结果
                                        for type_name, ids in child_result.deleted_entities.items():
                                            if type_name not in result.deleted_entities:
                                                result.deleted_entities[type_name] = []
                                            result.deleted_entities[type_name].extend(ids)
                                    else:
                                        logger.error(f"递归删除子区域失败: {child_id}, 原因: {child_result.message}")
                                except Exception as e:
                                    logger.error(f"递归删除子区域 {child_id} 时出错: {str(e)}")
                        else:
                            # 使用原生SQL删除依赖实体
                            if dependent_entity_ids:
                                try:
                                    # 获取表名
                                    table_name = ENTITY_TABLE_MAPPING.get(dependent_model)
                                    if not table_name:
                                        logger.error(f"未找到 {dependent_model.__name__} 的表名映射")
                                        continue

                                    # 构建删除的ID列表
                                    ids_str = ','.join([str(id) for id in dependent_entity_ids])

                                    # 执行原生SQL删除
                                    sql = text(f"DELETE FROM {table_name} WHERE id IN ({ids_str})")
                                    delete_result = db.session.execute(sql)
                                    deleted_count = delete_result.rowcount

                                    logger.info(f"使用SQL删除了 {deleted_count} 个依赖实体: {dependent_model.__name__}")

                                    # 记录删除的实体
                                    for entity_id in dependent_entity_ids:
                                        result.add_deleted(dependent_model, entity_id)

                                except Exception as e:
                                    error_msg = f"删除依赖实体 {dependent_model.__name__} 时出错: {str(e)}"
                                    logger.error(error_msg, exc_info=True)
                                    # 继续处理其他依赖实体，不中断整个删除过程

            # 使用原生SQL删除主实体
            logger.info(f"开始删除主实体: {entity_type.__name__} (ID: {entity_id})")

            # 获取表名
            table_name = ENTITY_TABLE_MAPPING.get(entity_type)
            if not table_name:
                error_msg = f"未找到 {entity_type.__name__} 的表名映射"
                logger.error(error_msg)
                result.success = False
                result.message = error_msg
                result.add_failed(entity_type, entity_id, error_msg)
                return result

            # 执行原生SQL删除
            sql = text(f"DELETE FROM {table_name} WHERE id = :entity_id")
            delete_result = db.session.execute(sql, {'entity_id': entity_id})
            deleted_count = delete_result.rowcount

            if deleted_count > 0:
                result.add_deleted(entity_type, entity_id)
                db.session.commit()
                logger.info(f"成功删除主实体: {entity_type.__name__} (ID: {entity_id})")
                result.success = True
                result.message = f"成功删除 {entity_type.__name__} (ID: {entity_id})"
            else:
                error_msg = f"主实体 {entity_type.__name__} (ID: {entity_id}) 未找到或已被删除"
                logger.warning(error_msg)
                result.success = False
                result.message = error_msg
                result.add_failed(entity_type, entity_id, error_msg)

        return result

    except Exception as e:
        error_msg = f"删除 {entity_type.__name__} (ID: {entity_id}) 时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        db.session.rollback()
        result.success = False
        result.message = error_msg
        result.add_failed(entity_type, entity_id, str(e))
        return result
