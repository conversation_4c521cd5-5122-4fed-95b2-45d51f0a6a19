{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .area-node {
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid #dee2e6 !important;
    }
    .area-node:hover {
        background-color: #f8f9fa !important;
        border-color: #007bff !important;
        transform: translateX(5px);
    }
    .area-node.bg-primary {
        background-color: #007bff !important;
        border-color: #007bff !important;
    }
    .area-node.bg-primary:hover {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .data-stats-card {
        transition: transform 0.2s ease;
    }
    .data-stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">超级删除工具</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h4><i class="fas fa-exclamation-triangle"></i> 警告</h4>
                        <p>此工具可以删除系统中的数据，并会自动处理相关联的数据。请谨慎使用！</p>
                        <p>建议在执行删除操作前，先使用"分析"功能查看删除操作的影响范围。</p>
                    </div>

                    <!-- 区域树形结构 -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-sitemap"></i> 区域树形结构</h5>
                                    <small class="text-muted">选择要删除的区域</small>
                                </div>
                                <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                                    <div id="areaTree">
                                        <div class="text-center">
                                            <i class="fas fa-spinner fa-spin"></i> 加载区域数据...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle"></i> 区域详细信息</h5>
                                    <small class="text-muted">查看选中区域的所有数据</small>
                                </div>
                                <div class="card-body">
                                    <div id="areaDetails" style="display: none;">
                                        <!-- 区域基本信息 -->
                                        <div class="alert alert-primary">
                                            <h6><i class="fas fa-building"></i> 选中区域：<span id="selectedAreaName"></span></h6>
                                            <p class="mb-0">ID: <span id="selectedAreaId"></span> | 层级: <span id="selectedAreaLevel"></span></p>
                                        </div>

                                        <!-- 数据统计卡片 -->
                                        <div class="row" id="dataStatsCards">
                                            <!-- 动态生成数据统计卡片 -->
                                        </div>

                                        <!-- 操作按钮 -->
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-warning" id="analyzeBtn">
                                                <i class="fas fa-search"></i> 分析删除影响
                                            </button>
                                            <button type="button" class="btn btn-danger" id="confirmDeleteBtn" style="display: none;">
                                                <i class="fas fa-trash"></i> 确认删除
                                            </button>
                                        </div>
                                    </div>

                                    <div id="noAreaSelected" class="text-center text-muted">
                                        <i class="fas fa-hand-pointer fa-3x mb-3"></i>
                                        <h5>请从左侧选择一个区域</h5>
                                        <p>选择区域后，这里将显示该区域的详细数据统计</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 隐藏的表单字段 -->
                    <form id="superDeleteForm" style="display: none;">
                        <input type="hidden" id="entityType" name="entity_type" value="area">
                        <input type="hidden" id="entityId" name="entity_id">
                    </form>
                            <div class="col-md-12 mt-3">
                                <div id="entityDetails" style="display: none;">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title" id="entityDetailsTitle">实体详情</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>属性</th>
                                                            <th>值</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="entityDetailsTable"></tbody>
                                                </table>
                                            </div>
                                            <div id="relatedEntities" class="mt-3">
                                                <h6>关联实体</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>关联类型</th>
                                                                <th>实体类型</th>
                                                                <th>数量</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="relatedEntitiesTable"></tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="forceDelete">强制删除</label>
                                    <div class="custom-control custom-switch mt-2">
                                        <input type="checkbox" class="custom-control-input" id="forceDelete" name="force">
                                        <label class="custom-control-label" for="forceDelete">忽略非必须依赖</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="button" id="analyzeBtn" class="btn btn-info mr-2" disabled>
                                    <i class="fas fa-search"></i> 分析
                                </button>
                                <button type="button" id="deleteBtn" class="btn btn-danger" disabled>
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </form>

                    <div id="analysisResult" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h4><i class="fas fa-chart-line"></i> 删除影响分析 - 按逻辑关系展示</h4>
                            </div>
                            <div class="card-body">
                                <div class="alert" id="analysisAlert"></div>

                                <!-- 删除流程步骤 -->
                                <div id="deleteStepsSection" style="display: none;">
                                    <h5><i class="fas fa-list-ol text-info"></i> 删除执行步骤</h5>
                                    <div class="row">
                                        <!-- 第一步：子区域 -->
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-primary">
                                                <div class="card-header bg-primary text-white">
                                                    <h6 class="mb-0"><i class="fas fa-sitemap"></i> 第1步：子区域</h6>
                                                </div>
                                                <div class="card-body" id="childAreasStep">
                                                    <p class="text-muted">检查中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 第二步：用户 -->
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white">
                                                    <h6 class="mb-0"><i class="fas fa-users"></i> 第2步：用户</h6>
                                                </div>
                                                <div class="card-body" id="usersStep">
                                                    <p class="text-muted">检查中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 第三步：供应商关系 -->
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-warning">
                                                <div class="card-header bg-warning text-white">
                                                    <h6 class="mb-0"><i class="fas fa-handshake"></i> 第3步：供应商关系</h6>
                                                </div>
                                                <div class="card-body" id="supplierRelationsStep">
                                                    <p class="text-muted">检查中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 第四步：菜单计划 -->
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-info">
                                                <div class="card-header bg-info text-white">
                                                    <h6 class="mb-0"><i class="fas fa-calendar-alt"></i> 第4步：菜单计划</h6>
                                                </div>
                                                <div class="card-body" id="menuPlansStep">
                                                    <p class="text-muted">检查中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 第五步：采购和库存 -->
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-secondary">
                                                <div class="card-header bg-secondary text-white">
                                                    <h6 class="mb-0"><i class="fas fa-warehouse"></i> 第5步：采购和库存</h6>
                                                </div>
                                                <div class="card-body" id="purchaseStockStep">
                                                    <p class="text-muted">检查中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 第六步：审计日志 -->
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-danger">
                                                <div class="card-header bg-danger text-white">
                                                    <h6 class="mb-0"><i class="fas fa-history"></i> 第6步：审计日志</h6>
                                                </div>
                                                <div class="card-body" id="auditLogsStep">
                                                    <p class="text-muted">检查中...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 阻止删除的实体 -->
                                <div id="blockedEntitiesSection" style="display: none;">
                                    <h5><i class="fas fa-ban text-warning"></i> 阻止删除的关联数据</h5>
                                    <div class="alert alert-warning" id="blockedEntitiesList">
                                        <!-- 动态生成的阻止列表 -->
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" id="confirmDeleteBtn" class="btn btn-danger" style="display: none;">
                                        <i class="fas fa-check"></i> 确认删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="deleteResult" style="display: none;">
                        <h4>删除结果</h4>
                        <div class="alert" id="deleteAlert"></div>

                        <div id="successEntitiesSection" style="display: none;">
                            <h5>成功删除的实体</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>实体类型</th>
                                            <th>数量</th>
                                            <th>ID列表</th>
                                        </tr>
                                    </thead>
                                    <tbody id="successEntitiesTable"></tbody>
                                </table>
                            </div>
                        </div>

                        <div id="failedEntitiesSection" style="display: none;">
                            <h5>删除失败的实体</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>实体类型</th>
                                            <th>实体ID</th>
                                            <th>失败原因</th>
                                        </tr>
                                    </thead>
                                    <tbody id="failedEntitiesTable"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 获取CSRF令牌
    var csrfToken = "{{ csrf_token() }}";

    // 设置AJAX默认头部包含CSRF令牌
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrfToken);
            }
        }
    });
    // 页面加载时加载区域树形结构
    $(document).ready(function() {
        loadAreaTree();
    });

    // 实体类型选择变化时启用搜索和加载实体列表
    $('#entityType').change(function() {
        const entityType = $(this).val();
        if (entityType) {
            $('#entitySearch').prop('disabled', false);
            $('#searchBtn').prop('disabled', false);

            // 更新搜索框的提示文本
            if (entityType === 'area') {
                $('#entitySearch').attr('placeholder', '输入学校/机构名称搜索');
            } else {
                $('#entitySearch').attr('placeholder', '输入名称或ID搜索');
            }
        } else {
            $('#entitySearch').prop('disabled', true);
            $('#searchBtn').prop('disabled', true);
        }
        // 清空搜索结果和选择
        $('#entitySearch').val('');
        $('#entityId').val('');
        $('#searchResults').hide().empty();
        $('#entityDetails').hide();
        updateButtonState();
    });

    // 刷新实体列表按钮点击事件
    $('#refreshEntityList').click(function() {
        const entityType = $('#entityType').val();
        if (entityType) {
            loadEntityList(entityType);
        }
    });

    // 实体列表选择变化事件
    $('#entityList').change(function() {
        const entityId = $(this).val();
        const entityName = $(this).find('option:selected').text();
        const entityType = $('#entityType').val();

        if (entityId) {
            $('#entityId').val(entityId);
            $('#entitySearch').val(entityName);
            updateButtonState();

            // 获取实体详情
            fetchEntityDetails(entityType, entityId);
        } else {
            $('#entityId').val('');
            $('#entitySearch').val('');
            $('#entityDetails').hide();
            updateButtonState();
        }
    });

    // 加载实体列表函数
    function loadEntityList(entityType) {
        $('#entityList').prop('disabled', true);
        $('#refreshEntityList').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

        $.ajax({
            url: "{{ url_for('admin_data.list_entities') }}",
            type: "GET",
            data: {
                entity_type: entityType
            },
            success: function(data) {
                $('#entityList').empty().append('<option value="">-- 请选择 --</option>');

                if (data.length > 0) {
                    data.forEach(function(entity) {
                        const option = $('<option></option>')
                            .val(entity.id)
                            .text(entity.name);
                        $('#entityList').append(option);
                    });
                } else {
                    $('#entityList').append('<option value="" disabled>没有可用的实体</option>');
                }

                $('#entityList').prop('disabled', false);
                $('#refreshEntityList').prop('disabled', false).html('<i class="fas fa-sync-alt"></i>');
            },
            error: function() {
                $('#entityList').empty()
                    .append('<option value="">加载失败</option>');
                $('#entityList').prop('disabled', false);
                $('#refreshEntityList').prop('disabled', false).html('<i class="fas fa-sync-alt"></i>');
            }
        });
    }

    // 搜索按钮点击事件
    $('#searchBtn').click(function() {
        searchEntities();
    });

    // 搜索框输入事件
    $('#entitySearch').on('input', function() {
        const query = $(this).val();
        if (query.length >= 2) {
            searchEntities();
        } else if (query.length === 0) {
            $('#searchResults').hide().empty();
            $('#entityId').val('');
            updateButtonState();
        }
    });

    // 搜索实体函数
    function searchEntities() {
        const entityType = $('#entityType').val();
        const query = $('#entitySearch').val();

        if (!entityType || query.length < 1) return;

        $.ajax({
            url: "{{ url_for('admin_data.search_entity') }}",
            type: "GET",
            data: {
                entity_type: entityType,
                query: query
            },
            success: function(data) {
                $('#searchResults').empty();

                if (data.length === 0) {
                    $('#searchResults').append('<div class="list-group-item">未找到匹配的实体</div>');
                } else {
                    data.forEach(function(entity) {
                        const item = $('<a href="#" class="list-group-item list-group-item-action"></a>')
                            .text(entity.name)
                            .data('id', entity.id)
                            .click(function(e) {
                                e.preventDefault();
                                $('#entitySearch').val(entity.name);
                                $('#entityId').val(entity.id);
                                $('#searchResults').hide();
                                updateButtonState();

                                // 获取实体详情
                                fetchEntityDetails(entityType, entity.id);
                            });
                        $('#searchResults').append(item);
                    });
                }

                $('#searchResults').show();
            },
            error: function() {
                $('#searchResults').empty()
                    .append('<div class="list-group-item list-group-item-danger">搜索出错</div>')
                    .show();
            }
        });
    }

    // 更新按钮状态
    function updateButtonState() {
        const entityType = $('#entityType').val();
        const entityId = $('#entityId').val();

        if (entityType && entityId) {
            $('#analyzeBtn').prop('disabled', false);
            $('#deleteBtn').prop('disabled', false);
        } else {
            $('#analyzeBtn').prop('disabled', true);
            $('#deleteBtn').prop('disabled', true);
        }
    }

    // 获取实体详情
    function fetchEntityDetails(entityType, entityId) {
        $.ajax({
            url: "{{ url_for('admin_data.entity_details') }}",
            type: "GET",
            data: {
                entity_type: entityType,
                entity_id: entityId
            },
            success: function(data) {
                if (data.success) {
                    // 显示实体详情
                    $('#entityDetailsTitle').text(data.entity_name + ' 详情');

                    // 清空并填充实体属性表格
                    $('#entityDetailsTable').empty();
                    for (const [key, value] of Object.entries(data.entity_attributes)) {
                        const row = $('<tr></tr>')
                            .append($('<td></td>').text(key))
                            .append($('<td></td>').text(value));
                        $('#entityDetailsTable').append(row);
                    }

                    // 清空并填充关联实体表格
                    $('#relatedEntitiesTable').empty();
                    for (const relation of data.related_entities) {
                        const row = $('<tr></tr>')
                            .append($('<td></td>').text(relation.relation_type))
                            .append($('<td></td>').text(relation.entity_type))
                            .append($('<td></td>').text(relation.count));
                        $('#relatedEntitiesTable').append(row);
                    }

                    // 显示详情区域
                    $('#entityDetails').show();
                } else {
                    // 隐藏详情区域
                    $('#entityDetails').hide();
                }
            },
            error: function() {
                // 隐藏详情区域
                $('#entityDetails').hide();
            }
        });
    }

    // 加载区域树形结构
    function loadAreaTree() {
        $.ajax({
            url: '/admin/super-delete/list-entities',
            method: 'GET',
            data: { entity_type: 'area' },
            success: function(data) {
                if (data.success) {
                    renderAreaTree(data.entities);
                } else {
                    $('#areaTree').html('<div class="alert alert-danger">加载区域数据失败</div>');
                }
            },
            error: function() {
                $('#areaTree').html('<div class="alert alert-danger">网络错误，无法加载区域数据</div>');
            }
        });
    }

    // 渲染区域树形结构
    function renderAreaTree(areas) {
        // 构建树形结构
        const tree = buildAreaTree(areas);
        const html = renderTreeNodes(tree);
        $('#areaTree').html(html);

        // 绑定点击事件
        $('.area-node').click(function() {
            const areaId = $(this).data('area-id');
            const areaName = $(this).data('area-name');
            const areaLevel = $(this).data('area-level');
            selectArea(areaId, areaName, areaLevel);
        });
    }

    // 构建树形结构
    function buildAreaTree(areas) {
        const tree = [];
        const map = {};

        // 创建映射
        areas.forEach(area => {
            map[area.id] = { ...area, children: [] };
        });

        // 构建树形结构
        areas.forEach(area => {
            if (area.parent_id && map[area.parent_id]) {
                map[area.parent_id].children.push(map[area.id]);
            } else {
                tree.push(map[area.id]);
            }
        });

        return tree;
    }

    // 渲染树形节点
    function renderTreeNodes(nodes, level = 0) {
        let html = '';
        nodes.forEach(node => {
            const indent = '&nbsp;'.repeat(level * 4);
            const hasChildren = node.children && node.children.length > 0;
            const icon = hasChildren ? 'fas fa-folder' : 'fas fa-building';
            const color = level === 0 ? 'text-primary' : (level === 1 ? 'text-success' : 'text-info');

            html += `
                <div class="area-node mb-1 p-2 border rounded cursor-pointer"
                     data-area-id="${node.id}"
                     data-area-name="${node.name}"
                     data-area-level="${level + 1}"
                     style="margin-left: ${level * 20}px;">
                    <i class="${icon} ${color}"></i>
                    ${indent}<strong>${node.name}</strong>
                    <small class="text-muted">(ID: ${node.id})</small>
                </div>
            `;

            if (hasChildren) {
                html += renderTreeNodes(node.children, level + 1);
            }
        });
        return html;
    }

    // 选择区域
    function selectArea(areaId, areaName, areaLevel) {
        // 更新选中状态
        $('.area-node').removeClass('bg-primary text-white');
        $(`.area-node[data-area-id="${areaId}"]`).addClass('bg-primary text-white');

        // 更新区域信息
        $('#selectedAreaName').text(areaName);
        $('#selectedAreaId').text(areaId);
        $('#selectedAreaLevel').text(areaLevel);
        $('#entityId').val(areaId);

        // 显示区域详情
        $('#noAreaSelected').hide();
        $('#areaDetails').show();

        // 加载区域数据统计
        loadAreaDataStats(areaId);
    }

    // 加载区域数据统计
    function loadAreaDataStats(areaId) {
        $.ajax({
            url: '/admin/super-delete/entity-details',
            method: 'GET',
            data: {
                entity_type: 'area',
                entity_id: areaId
            },
            success: function(data) {
                if (data.success) {
                    renderDataStatsCards(data.related_entities);
                } else {
                    $('#dataStatsCards').html('<div class="col-12"><div class="alert alert-warning">无法加载数据统计</div></div>');
                }
            },
            error: function() {
                $('#dataStatsCards').html('<div class="col-12"><div class="alert alert-danger">加载数据统计失败</div></div>');
            }
        });
    }

    // 渲染数据统计卡片
    function renderDataStatsCards(relatedEntities) {
        let html = '';

        // 定义数据类型的图标和颜色
        const typeConfig = {
            '子区域': { icon: 'fas fa-sitemap', color: 'primary' },
            '用户': { icon: 'fas fa-users', color: 'success' },
            '供应商关系': { icon: 'fas fa-handshake', color: 'warning' },
            '菜单计划': { icon: 'fas fa-calendar-alt', color: 'info' },
            '采购订单': { icon: 'fas fa-shopping-cart', color: 'secondary' },
            '仓库': { icon: 'fas fa-warehouse', color: 'dark' },
            '审计日志': { icon: 'fas fa-history', color: 'danger' }
        };

        if (relatedEntities && relatedEntities.length > 0) {
            relatedEntities.forEach(entity => {
                const config = typeConfig[entity.relation_type] || { icon: 'fas fa-database', color: 'secondary' };
                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card border-${config.color}">
                            <div class="card-body text-center">
                                <i class="${config.icon} fa-2x text-${config.color} mb-2"></i>
                                <h6 class="card-title">${entity.relation_type}</h6>
                                <h4 class="text-${config.color}">${entity.count}</h4>
                                <small class="text-muted">${entity.entity_type}</small>
                                <br>
                                <small class="badge badge-${entity.cascade === '是' ? 'success' : 'warning'}">
                                    ${entity.cascade === '是' ? '级联删除' : '保留数据'}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
        } else {
            html = `
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i>
                        该区域下暂无相关数据
                    </div>
                </div>
            `;
        }

        $('#dataStatsCards').html(html);
    }

    // 显示删除步骤
    function displayDeletionSteps(stepsData) {
        // 第1步：子区域
        updateStepDisplay('childAreasStep', stepsData.child_areas);

        // 第2步：用户
        updateStepDisplay('usersStep', stepsData.users);

        // 第3步：供应商关系
        updateStepDisplay('supplierRelationsStep', stepsData.supplier_relations);

        // 第4步：菜单计划
        updateStepDisplay('menuPlansStep', stepsData.menu_plans);

        // 第5步：采购和库存
        updateStepDisplay('purchaseStockStep', stepsData.purchase_stock);

        // 第6步：审计日志
        updateStepDisplay('auditLogsStep', stepsData.audit_logs);
    }

    // 更新单个步骤的显示
    function updateStepDisplay(stepId, stepData) {
        const stepElement = $('#' + stepId);

        // 步骤ID到步骤类型的映射
        const stepTypeMap = {
            'childAreasStep': 'child_areas',
            'usersStep': 'users',
            'supplierRelationsStep': 'supplier_relations',
            'menuPlansStep': 'menu_plans',
            'purchaseStockStep': 'purchase_stock',
            'auditLogsStep': 'audit_logs'
        };

        const stepType = stepTypeMap[stepId];

        if (stepData.count > 0) {
            let html = `<div class="alert alert-info mb-2">
                <strong>总计：${stepData.count} 条记录</strong>
                <button class="btn btn-sm btn-danger float-right" onclick="executeStep('${stepType}')">
                    <i class="fas fa-trash"></i> 删除此步骤
                </button>
                <div class="clearfix"></div>
            </div>`;

            stepData.entities.forEach(function(entity) {
                html += `<div class="mb-2">
                    <span class="badge badge-primary">${entity.type_name}</span>
                    <span class="text-muted">${entity.count} 条</span>
                    <br><small class="text-muted">ID: ${entity.ids.slice(0, 5).join(', ')}${entity.ids.length > 5 ? '...' : ''}</small>
                </div>`;
            });

            stepElement.html(html);
        } else {
            stepElement.html('<p class="text-success"><i class="fas fa-check"></i> 无需处理</p>');
        }
    }

    // 分析按钮点击事件
    $('#analyzeBtn').click(function() {
        const entityId = $('#entityId').val();

        if (!entityId) {
            alert('请先选择要删除的区域');
            return;
        }

        $.ajax({
            url: "{{ url_for('admin_data.analyze') }}",
            type: "POST",
            data: {
                entity_type: 'area',
                entity_id: entityId,
                force: false,
                csrf_token: csrfToken
            },
            beforeSend: function() {
                $('#analyzeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 分析中...');
                $('#deleteResult').hide();
            },
            success: function(data) {
                $('#analyzeBtn').prop('disabled', false).html('<i class="fas fa-search"></i> 分析');

                // 显示分析结果
                $('#analysisResult').show();

                if (data.success) {
                    $('#analysisAlert').removeClass('alert-danger').addClass('alert-success').text(data.message);
                    $('#confirmDeleteBtn').show();
                } else {
                    $('#analysisAlert').removeClass('alert-success').addClass('alert-danger').text(data.message);
                    $('#confirmDeleteBtn').hide();
                }

                // 显示按步骤分类的删除数据
                if (data.steps_data) {
                    displayDeletionSteps(data.steps_data);
                    $('#deleteStepsSection').show();
                } else {
                    $('#deleteStepsSection').hide();
                }

                // 显示阻止删除的依赖
                if (data.blocked_by && data.blocked_by.length > 0) {
                    let blockedText = '';
                    data.blocked_by.forEach(function(entity) {
                        blockedText += `${entity.type_name} (ID: ${entity.id}): ${JSON.stringify(entity.blocking_info)}\n`;
                    });
                    $('#blockedEntitiesList').text(blockedText);
                    $('#blockedEntitiesSection').show();
                } else {
                    $('#blockedEntitiesSection').hide();
                }
            },
            error: function() {
                $('#analyzeBtn').prop('disabled', false).html('<i class="fas fa-search"></i> 分析');
                $('#analysisResult').show();
                $('#analysisAlert').removeClass('alert-success').addClass('alert-danger').text('分析请求失败');
                $('#confirmDeleteBtn').hide();
                $('#deletedEntitiesSection').hide();
                $('#blockedEntitiesSection').hide();
            }
        });
    });

    // 删除按钮点击事件
    $('#deleteBtn, #confirmDeleteBtn').click(function() {
        if (!confirm('确定要执行删除操作吗？此操作不可撤销！')) {
            return;
        }

        const entityId = $('#entityId').val();

        if (!entityId) {
            alert('请先选择要删除的区域');
            return;
        }

        $.ajax({
            url: "{{ url_for('admin_data.execute') }}",
            type: "POST",
            data: {
                entity_type: 'area',
                entity_id: entityId,
                force: false,
                csrf_token: csrfToken
            },
            beforeSend: function() {
                $('#deleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...');
                $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...');
                $('#analysisResult').hide();
            },
            success: function(data) {
                $('#deleteBtn').prop('disabled', false).html('<i class="fas fa-trash"></i> 删除');
                $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-check"></i> 确认删除');

                // 显示删除结果
                $('#deleteResult').show();

                if (data.success) {
                    $('#deleteAlert').removeClass('alert-danger').addClass('alert-success').text(data.message);
                } else {
                    $('#deleteAlert').removeClass('alert-success').addClass('alert-danger').text(data.message);
                }

                // 显示成功删除的实体
                if (data.deleted_entities && data.deleted_entities.length > 0) {
                    $('#successEntitiesTable').empty();
                    data.deleted_entities.forEach(function(entity) {
                        const row = $('<tr></tr>')
                            .append($('<td></td>').text(entity.type_name))
                            .append($('<td></td>').text(entity.count))
                            .append($('<td></td>').text(entity.ids.join(', ')));
                        $('#successEntitiesTable').append(row);
                    });
                    $('#successEntitiesSection').show();
                } else {
                    $('#successEntitiesSection').hide();
                }

                // 显示删除失败的实体
                if (data.failed_entities && data.failed_entities.length > 0) {
                    $('#failedEntitiesTable').empty();
                    data.failed_entities.forEach(function(entity) {
                        const row = $('<tr></tr>')
                            .append($('<td></td>').text(entity.type_name))
                            .append($('<td></td>').text(entity.id))
                            .append($('<td></td>').text(entity.reason));
                        $('#failedEntitiesTable').append(row);
                    });
                    $('#failedEntitiesSection').show();
                } else {
                    $('#failedEntitiesSection').hide();
                }

                // 清空表单并刷新页面
                if (data.success) {
                    $('#entitySearch').val('');
                    $('#entityId').val('');
                    updateButtonState();

                    // 重新加载区域树和数据
                    loadAreaTree();
                    $('#dataStatsCards').empty();
                    $('#entityDetails').hide();
                    $('#analysisResult').hide();

                    // 显示成功消息后自动隐藏
                    setTimeout(function() {
                        $('#deleteResult').fadeOut();
                    }, 3000);
                }
            },
            error: function() {
                $('#deleteBtn').prop('disabled', false).html('<i class="fas fa-trash"></i> 删除');
                $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-check"></i> 确认删除');
                $('#deleteResult').show();
                $('#deleteAlert').removeClass('alert-success').addClass('alert-danger').text('删除请求失败');
                $('#successEntitiesSection').hide();
                $('#failedEntitiesSection').hide();
            }
        });
    });

    // 执行分步删除
    function executeStep(stepType) {
        const entityId = $('#entityId').val();

        if (!entityId) {
            alert('请先选择要删除的区域');
            return;
        }

        const stepNames = {
            'child_areas': '子区域',
            'users': '用户',
            'supplier_relations': '供应商关系',
            'menu_plans': '菜单计划',
            'purchase_stock': '采购和库存',
            'audit_logs': '审计日志'
        };

        const stepName = stepNames[stepType] || stepType;

        if (!confirm(`确定要删除 "${stepName}" 相关的所有数据吗？此操作不可撤销！`)) {
            return;
        }

        $.ajax({
            url: "{{ url_for('admin_data.execute_step') }}",
            type: "POST",
            data: {
                entity_type: 'area',
                entity_id: entityId,
                step_type: stepType,
                force: false,
                csrf_token: csrfToken
            },
            beforeSend: function() {
                // 禁用所有删除按钮
                $('.step-delete-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...');
            },
            success: function(data) {
                // 恢复按钮状态
                $('.step-delete-btn').prop('disabled', false).html('<i class="fas fa-trash"></i> 删除此步骤');

                if (data.success) {
                    alert(`成功删除 "${stepName}" 相关数据！`);

                    // 重新分析以更新显示
                    $('#analyzeBtn').click();

                    // 重新加载区域树和数据
                    loadAreaTree();
                    $('#dataStatsCards').empty();
                    loadAreaDataStats(entityId);
                } else {
                    alert(`删除 "${stepName}" 失败：${data.message}`);
                }
            },
            error: function() {
                // 恢复按钮状态
                $('.step-delete-btn').prop('disabled', false).html('<i class="fas fa-trash"></i> 删除此步骤');
                alert(`删除 "${stepName}" 请求失败，请重试`);
            }
        });
    }
});
</script>
{% endblock %}
